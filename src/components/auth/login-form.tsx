'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { useToast } from '@/components/ui/toast'
import { useAuth } from '@/hooks/use-auth'
import { LoginFormData } from '@/types/auth'
import { SocialAuthSection } from './social-auth'
import { testConnection } from '@/lib/supabase/auth'

interface LoginFormProps {
  onSuccess?: () => void
  redirectTo?: string
}

export const LoginForm = ({
  onSuccess,
  redirectTo = '/gallery',
}: LoginFormProps) => {
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
  })
  const [isLoading, setIsLoading] = useState(false)

  const router = useRouter()
  const searchParams = useSearchParams()
  const { signIn, signInWithSocial } = useAuth()
  const { addToast } = useToast()

  // Handle authentication errors from URL params
  React.useEffect(() => {
    const error = searchParams.get('error')
    const message = searchParams.get('message')

    if (error && message) {
      // Don't show error for 'no_code' errors that happen during OAuth flow
      if (error !== 'no_code') {
        addToast({
          type: 'error',
          title: 'Authentication failed',
          description: decodeURIComponent(message),
        })
      }

      // Clean up URL params
      const url = new URL(window.location.href)
      url.searchParams.delete('error')
      url.searchParams.delete('message')
      window.history.replaceState({}, '', url.toString())
    }
  }, [searchParams, addToast])

  // Get redirect URL from search params or use default
  const finalRedirectTo = searchParams.get('redirect') || redirectTo

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Test connection first
      const connectionOk = await testConnection()
      if (!connectionOk) {
        throw new Error(
          'Unable to connect to authentication service. Please check your internet connection and try again.'
        )
      }

      await signIn(formData.email, formData.password)
      addToast({
        type: 'success',
        title: 'Login successful',
        description: 'Welcome back!',
      })

      if (onSuccess) {
        onSuccess()
      } else {
        router.push(finalRedirectTo)
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Login failed',
        description:
          error instanceof Error
            ? error.message
            : 'An error occurred during login',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const isValid = formData.email && formData.password

  return (
    <Card className='w-full max-w-md mx-auto'>
      <CardHeader className='space-y-1'>
        <CardTitle className='text-2xl text-center'>Sign in</CardTitle>
        <CardDescription className='text-center'>
          Enter your email and password to access your gallery
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className='space-y-4'>
          <div className='space-y-2'>
            <label htmlFor='email' className='text-sm font-medium'>
              Email
            </label>
            <Input
              id='email'
              name='email'
              type='email'
              placeholder='Enter your email'
              value={formData.email}
              onChange={handleChange}
              required
              disabled={isLoading}
            />
          </div>

          <div className='space-y-2'>
            <label htmlFor='password' className='text-sm font-medium'>
              Password
            </label>
            <Input
              id='password'
              name='password'
              type='password'
              placeholder='Enter your password'
              value={formData.password}
              onChange={handleChange}
              required
              disabled={isLoading}
            />
          </div>

          <Button
            type='submit'
            className='w-full'
            disabled={!isValid || isLoading}
          >
            {isLoading ? 'Signing in...' : 'Sign in'}
          </Button>
        </form>

        <SocialAuthSection disabled={isLoading} showDivider={true} />

        <div className='mt-4 text-center text-sm'>
          <span className='text-muted-foreground'>
            Don&apos;t have an account?{' '}
          </span>
          <Button
            variant='link'
            className='p-0 h-auto font-normal'
            onClick={() => router.push('/signup')}
            disabled={isLoading}
          >
            Sign up
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
