import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { ensureUserProfile } from '@/lib/supabase/auth'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const origin = requestUrl.origin
  const redirectTo = requestUrl.searchParams.get('redirect_to')?.toString()
  const error_param = requestUrl.searchParams.get('error')
  const error_description = requestUrl.searchParams.get('error_description')

  console.log('🔄 OAuth callback received:', {
    hasCode: !!code,
    error: error_param,
    error_description,
    origin,
    redirectTo,
    fullUrl: request.url,
  })

  // Handle OAuth errors from provider
  if (error_param) {
    console.error('❌ OAuth provider error:', {
      error_param,
      error_description,
    })
    return NextResponse.redirect(
      `${origin}/login?error=oauth_provider_error&details=${encodeURIComponent(error_description || error_param)}`
    )
  }

  if (code) {
    const supabase = await createClient()
    try {
      console.log(
        '🔄 Processing OAuth callback with code:',
        code.substring(0, 10) + '...'
      )

      const { data, error } = await supabase.auth.exchangeCodeForSession(code)

      if (error) {
        console.error('❌ OAuth exchange error:', {
          message: error.message,
          status: error.status,
          details: error,
        })
        return NextResponse.redirect(
          `${origin}/login?error=oauth_exchange_error&details=${encodeURIComponent(error.message)}`
        )
      }

      if (data.user) {
        console.log('✅ OAuth user created/authenticated:', {
          id: data.user.id,
          email: data.user.email,
          provider: data.user.app_metadata?.provider,
          user_metadata: data.user.user_metadata,
          app_metadata: data.user.app_metadata,
          created_at: data.user.created_at,
          last_sign_in_at: data.user.last_sign_in_at,
        })

        // Check if this is a new user vs returning user
        const isNewUser = data.user.created_at === data.user.last_sign_in_at
        console.log(`👤 User type: ${isNewUser ? 'NEW' : 'RETURNING'} user`)

        // Force profile creation for OAuth users (especially GitHub)
        try {
          console.log('🔧 Force creating profile for OAuth user...')
          await ensureUserProfile(data.user as any)
          console.log('✅ Profile ensured for OAuth user')

          // Additional delay to ensure database operations complete
          await new Promise(resolve => setTimeout(resolve, 2000))
        } catch (profileError) {
          console.error('❌ Failed to ensure profile:', profileError)
          // Continue anyway - don't block the login
        }
      } else {
        console.warn('⚠️ OAuth exchange succeeded but no user data returned')
      }
    } catch (error) {
      console.error('💥 OAuth callback error:', error)
      return NextResponse.redirect(
        `${origin}/login?error=callback_error&details=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`
      )
    }
  } else {
    console.warn('⚠️ OAuth callback received without code parameter')
    return NextResponse.redirect(`${origin}/login?error=no_code`)
  }

  if (redirectTo) {
    return NextResponse.redirect(`${origin}${redirectTo}`)
  }

  // URL to redirect to after sign up/login process completes
  return NextResponse.redirect(`${origin}/gallery`)
}
