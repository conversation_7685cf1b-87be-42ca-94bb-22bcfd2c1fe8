import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const origin = requestUrl.origin
  const redirectTo = requestUrl.searchParams.get('redirect_to')?.toString()

  if (code) {
    const supabase = await createClient()
    const { data, error } = await supabase.auth.exchangeCodeForSession(code)

    // Ensure profile creation after successful OAuth authentication
    if (data?.user && !error) {
      console.log('🔧 OAuth callback: ensuring profile for user:', data.user.id)

      try {
        // Check if profile exists
        const { data: existingProfile } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', data.user.id)
          .single()

        if (!existingProfile) {
          // Extract user data from auth metadata
          const fullName =
            data.user.user_metadata?.full_name ||
            data.user.user_metadata?.name ||
            data.user.user_metadata?.user_name ||
            ''

          const avatarUrl = data.user.user_metadata?.avatar_url || ''
          const provider = data.user.app_metadata?.provider || 'email'

          console.log('📝 Creating profile via callback:', {
            id: data.user.id,
            email: data.user.email,
            fullName,
            provider,
            hasAvatar: !!avatarUrl,
          })

          // Create profile
          const { error: profileError } = await supabase
            .from('profiles')
            .insert({
              id: data.user.id,
              email: data.user.email,
              full_name: fullName,
              avatar_url: avatarUrl,
              provider: provider,
            })

          if (profileError) {
            console.error(
              '❌ Failed to create profile in callback:',
              profileError
            )
          } else {
            console.log('✅ Profile created successfully in callback')
          }
        } else {
          console.log('✅ Profile already exists for OAuth user')
        }
      } catch (profileError) {
        console.error('❌ Error ensuring profile in callback:', profileError)
      }
    }
  }

  if (redirectTo) {
    return NextResponse.redirect(`${origin}${redirectTo}`)
  }

  // URL to redirect to after sign up/login process completes
  return NextResponse.redirect(`${origin}/gallery`)
}
