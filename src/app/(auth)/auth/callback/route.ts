import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { ensureUserProfile } from '@/lib/supabase/auth'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const origin = requestUrl.origin
  const redirectTo = requestUrl.searchParams.get('redirect_to')?.toString()

  if (code) {
    const supabase = await createClient()
    try {
      console.log(
        '🔄 Processing OAuth callback with code:',
        code.substring(0, 10) + '...'
      )

      const { data, error } = await supabase.auth.exchangeCodeForSession(code)

      if (error) {
        console.error('❌ OAuth exchange error:', error)
        return NextResponse.redirect(`${origin}/login?error=oauth_error`)
      }

      if (data.user) {
        console.log('✅ OAuth user created/authenticated:', {
          id: data.user.id,
          email: data.user.email,
          provider: data.user.app_metadata?.provider,
          user_metadata: data.user.user_metadata,
        })

        // Ensure profile exists (fallback if trigger doesn't work)
        try {
          await ensureUserProfile(data.user as any)
          console.log('✅ Profile ensured for OAuth user')
        } catch (profileError) {
          console.error('❌ Failed to ensure profile:', profileError)
        }
      }
    } catch (error) {
      console.error('💥 OAuth callback error:', error)
      return NextResponse.redirect(`${origin}/login?error=callback_error`)
    }
  }

  if (redirectTo) {
    return NextResponse.redirect(`${origin}${redirectTo}`)
  }

  // URL to redirect to after sign up/login process completes
  return NextResponse.redirect(`${origin}/gallery`)
}
