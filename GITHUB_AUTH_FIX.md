# 🔧 GitHub Authentication Fix Guide

## 🎯 Problem Identified

Your GitHub authentication can login successfully, but **user profiles are not being inserted into the database tables**. This is because the `handle_new_user()` trigger function is not properly set up in your cloud Supabase instance.

## 📊 Current State

- ✅ GitHub OAuth login works (user can authenticate)
- ❌ No profiles are created in the `profiles` table (0 profiles found)
- ❌ The `handle_new_user()` trigger is missing or not working

## 🛠️ Solution: 3-Step Fix

### Step 1: Set up the Database Trigger

1. **Open your Supabase Dashboard**: https://supabase.com/dashboard
2. **Navigate to**: Your Project → SQL Editor
3. **Copy and run** the SQL from `scripts/setup-auth-trigger.sql`:

```sql
-- 1. Create the handle_new_user function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  BEGIN
    INSERT INTO public.profiles (id, email, full_name, avatar_url, provider)
    VALUES (
      NEW.id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
      NEW.raw_user_meta_data->>'avatar_url',
      COALESCE(NEW.raw_app_meta_data->>'provider', 'email')
    );
  EXCEPTION
    WHEN OTHERS THEN
      RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
  END;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- 2. Create the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 3. Fix existing users without profiles
INSERT INTO public.profiles (id, email, full_name, avatar_url, provider)
SELECT 
  u.id,
  u.email,
  COALESCE(u.raw_user_meta_data->>'full_name', u.raw_user_meta_data->>'name'),
  u.raw_user_meta_data->>'avatar_url',
  COALESCE(u.raw_app_meta_data->>'provider', 'email')
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
WHERE p.id IS NULL;
```

### Step 2: Test the Fix

1. **Run the verification script**:
   ```bash
   node scripts/simple-auth-test.js
   ```

2. **You should see**:
   ```
   ✅ Found X profiles:
      1. <EMAIL> (github) - timestamp
   ```

### Step 3: Test GitHub Authentication

1. **Try logging in with GitHub** again
2. **Check the browser console** for these logs:
   ```
   🔧 Creating profile for user: {id: "...", email: "...", provider: "github"}
   ✅ Profile created successfully for user: ...
   ```

## 🔍 Verification Queries

Run these in Supabase SQL Editor to verify everything is working:

```sql
-- Check if trigger exists
SELECT tgname, tgrelid::regclass, tgfoid::regproc 
FROM pg_trigger 
WHERE tgname = 'on_auth_user_created';

-- Check users vs profiles count
SELECT 
  (SELECT COUNT(*) FROM auth.users) as users_count,
  (SELECT COUNT(*) FROM public.profiles) as profiles_count;

-- Show recent users and profiles
SELECT 
  u.id,
  u.email,
  u.created_at as user_created,
  u.raw_app_meta_data->>'provider' as auth_provider,
  p.created_at as profile_created,
  p.provider as profile_provider
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
ORDER BY u.created_at DESC
LIMIT 10;
```

## 🚀 Improvements Made

### Enhanced Authentication Callback
- **File**: `src/app/(auth)/auth/callback/route.ts`
- **Improvement**: Now ensures profile creation during OAuth callback
- **Fallback**: If trigger fails, callback creates profile manually

### Enhanced Auth Hook
- **File**: `src/hooks/use-auth.tsx`
- **Improvement**: Added `ensureUserProfile()` function
- **Behavior**: Checks and creates profiles during auth state changes

### Backup Authentication Service
- **File**: `src/lib/supabase/auth-enhanced.ts`
- **Purpose**: Enhanced auth service with profile creation
- **Usage**: Can be used if current auth service needs replacement

## 🎯 Expected Results

After applying this fix:

1. **Existing GitHub users** will have profiles created
2. **New GitHub users** will automatically get profiles via trigger
3. **Fallback protection** ensures profiles are created even if trigger fails
4. **Google authentication** continues to work as before

## 🐛 Troubleshooting

### If the trigger still doesn't work:
1. Check Supabase logs for errors
2. Verify the trigger exists with the verification queries
3. Test with a new GitHub authentication

### If profiles are still not created:
1. Check browser console for profile creation logs
2. The enhanced callback should create profiles as fallback
3. Run the verification script again

## ✅ Success Criteria

Your GitHub authentication is fixed when:

- ✅ GitHub login works
- ✅ Profile is created in `profiles` table
- ✅ User can access protected routes
- ✅ User data persists between sessions

Run `node scripts/simple-auth-test.js` to verify the fix!
