#!/usr/bin/env node

/**
 * Simple Authentication Test Script
 *
 * This script tests authentication and profile creation without requiring admin access
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env' })

// Initialize Supabase client with anon key
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

class SimpleAuthTest {
  constructor() {
    console.log('🔧 Testing authentication with:', {
      url: process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    })
  }

  async testConnection() {
    console.log('\n🔗 Testing Supabase connection...')
    try {
      const { data, error } = await supabase.auth.getSession()
      if (error) {
        console.log('❌ Connection error:', error.message)
        return false
      }
      console.log('✅ Connection successful')
      return true
    } catch (error) {
      console.log('❌ Connection failed:', error.message)
      return false
    }
  }

  async testProfilesTable() {
    console.log('\n📋 Testing profiles table access...')
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, email, provider, created_at')
        .limit(5)

      if (error) {
        console.log('❌ Profiles table error:', error.message)
        return false
      }

      console.log(`✅ Found ${data.length} profiles:`)
      data.forEach((profile, index) => {
        console.log(
          `   ${index + 1}. ${profile.email} (${profile.provider}) - ${profile.created_at}`
        )
      })
      return data
    } catch (error) {
      console.log('❌ Profiles table exception:', error.message)
      return false
    }
  }

  async runTests() {
    console.log('🚀 Starting simple authentication tests...\n')

    const connectionOk = await this.testConnection()
    if (!connectionOk) {
      console.log('\n❌ Cannot proceed - connection failed')
      return
    }

    const profiles = await this.testProfilesTable()

    console.log('\n=== RECOMMENDATIONS ===')

    if (!profiles || profiles.length === 0) {
      console.log('⚠️  No profiles found. This suggests:')
      console.log('   1. The handle_new_user trigger is not working')
      console.log('   2. Or no users have been created yet')
      console.log(
        '   3. Or the trigger was not properly set up in the cloud database'
      )
    } else {
      console.log(`✅ Found ${profiles.length} profiles`)

      // Check for missing GitHub profiles
      const githubProfiles = profiles.filter(p => p.provider === 'github')
      const googleProfiles = profiles.filter(p => p.provider === 'google')

      console.log(`   - Google profiles: ${googleProfiles.length}`)
      console.log(`   - GitHub profiles: ${githubProfiles.length}`)

      if (googleProfiles.length > 0 && githubProfiles.length === 0) {
        console.log('⚠️  Google works but no GitHub profiles found')
        console.log(
          '   This suggests GitHub auth might not be triggering profile creation'
        )
      }
    }

    console.log('\n=== NEXT STEPS ===')
    console.log('1. Check Supabase dashboard for the handle_new_user function')
    console.log('2. Check if the trigger exists on auth.users table')
    console.log('3. Test GitHub authentication and check logs')
    console.log('4. Consider manually running the migration script')

    console.log('\n✅ Test complete')
  }
}

// Run the tests
if (require.main === module) {
  const tester = new SimpleAuthTest()
  tester.runTests().catch(error => {
    console.error('❌ Test failed:', error)
    process.exit(1)
  })
}

module.exports = SimpleAuthTest
