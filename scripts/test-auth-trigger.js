#!/usr/bin/env node

/**
 * Test Authentication Trigger Script
 *
 * This script tests if the handle_new_user trigger is working properly
 * and checks the database state for authentication-related tables
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env' })

// Initialize Supabase client with service role for admin access
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

class AuthTriggerTest {
  constructor() {
    this.testResults = []
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString()
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️'
    console.log(`${prefix} [${timestamp}] ${message}`)
    this.testResults.push({ timestamp, message, type })
  }

  async checkTriggerFunction() {
    this.log('Checking if handle_new_user function exists...')

    try {
      const { data, error } = await supabase.rpc('exec_sql', {
        sql: `
          SELECT 
            proname as function_name,
            prosrc as function_source
          FROM pg_proc 
          WHERE proname = 'handle_new_user';
        `,
      })

      if (error) {
        this.log(`Error checking function: ${error.message}`, 'error')
        return false
      }

      if (data && data.length > 0) {
        this.log('handle_new_user function exists', 'success')
        return true
      } else {
        this.log('handle_new_user function NOT found', 'error')
        return false
      }
    } catch (error) {
      this.log(`Exception checking function: ${error.message}`, 'error')
      return false
    }
  }

  async checkTrigger() {
    this.log('Checking if trigger exists on auth.users...')

    try {
      const { data, error } = await supabase.rpc('exec_sql', {
        sql: `
          SELECT 
            tgname as trigger_name,
            tgrelid::regclass as table_name,
            tgfoid::regproc as function_name
          FROM pg_trigger 
          WHERE tgname = 'on_auth_user_created';
        `,
      })

      if (error) {
        this.log(`Error checking trigger: ${error.message}`, 'error')
        return false
      }

      if (data && data.length > 0) {
        this.log('on_auth_user_created trigger exists', 'success')
        return true
      } else {
        this.log('on_auth_user_created trigger NOT found', 'error')
        return false
      }
    } catch (error) {
      this.log(`Exception checking trigger: ${error.message}`, 'error')
      return false
    }
  }

  async checkProfilesTable() {
    this.log('Checking profiles table structure...')

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .limit(1)

      if (error && error.code !== 'PGRST116') {
        this.log(`Error accessing profiles table: ${error.message}`, 'error')
        return false
      }

      this.log('profiles table is accessible', 'success')
      return true
    } catch (error) {
      this.log(`Exception checking profiles table: ${error.message}`, 'error')
      return false
    }
  }

  async checkRecentUsers() {
    this.log('Checking recent auth.users entries...')

    try {
      const { data, error } = await supabase.rpc('exec_sql', {
        sql: `
          SELECT 
            id,
            email,
            created_at,
            raw_app_meta_data->>'provider' as provider,
            raw_user_meta_data->>'full_name' as full_name,
            raw_user_meta_data->>'name' as name,
            raw_user_meta_data->>'avatar_url' as avatar_url
          FROM auth.users 
          ORDER BY created_at DESC 
          LIMIT 5;
        `,
      })

      if (error) {
        this.log(`Error checking auth.users: ${error.message}`, 'error')
        return false
      }

      this.log(`Found ${data.length} recent users in auth.users`, 'success')
      data.forEach((user, index) => {
        this.log(
          `  User ${index + 1}: ${user.email} (${user.provider}) - ${user.created_at}`
        )
      })
      return data
    } catch (error) {
      this.log(`Exception checking auth.users: ${error.message}`, 'error')
      return false
    }
  }

  async checkProfiles() {
    this.log('Checking profiles table entries...')

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5)

      if (error) {
        this.log(`Error checking profiles: ${error.message}`, 'error')
        return false
      }

      this.log(`Found ${data.length} profiles in profiles table`, 'success')
      data.forEach((profile, index) => {
        this.log(
          `  Profile ${index + 1}: ${profile.email} (${profile.provider}) - ${profile.created_at}`
        )
      })
      return data
    } catch (error) {
      this.log(`Exception checking profiles: ${error.message}`, 'error')
      return false
    }
  }

  async createExecSqlFunction() {
    this.log('Creating exec_sql function if needed...')

    try {
      const { error } = await supabase.rpc('exec_sql', {
        sql: `
          CREATE OR REPLACE FUNCTION exec_sql(sql text)
          RETURNS TABLE(result json)
          LANGUAGE plpgsql
          SECURITY DEFINER
          AS $$
          DECLARE
            rec RECORD;
            result_array json[];
          BEGIN
            result_array := '{}';
            FOR rec IN EXECUTE sql LOOP
              result_array := array_append(result_array, row_to_json(rec));
            END LOOP;
            RETURN QUERY SELECT array_to_json(result_array);
          END;
          $$;
        `,
      })

      if (error) {
        this.log(
          `Could not create exec_sql function: ${error.message}`,
          'error'
        )
        // Try a simpler version
        const { error: simpleError } = await supabase.rpc('exec_sql', {
          sql: 'SELECT 1 as test',
        })

        if (simpleError) {
          this.log(
            'exec_sql function does not exist and cannot be created',
            'error'
          )
          return false
        } else {
          this.log('exec_sql function already exists', 'success')
          return true
        }
      } else {
        this.log('exec_sql function created successfully', 'success')
        return true
      }
    } catch (error) {
      this.log(
        `Exception creating exec_sql function: ${error.message}`,
        'error'
      )
      return false
    }
  }

  async runTests() {
    this.log('Starting authentication trigger tests...')

    // First try to create exec_sql function for SQL queries
    await this.createExecSqlFunction()

    // Check database components
    await this.checkProfilesTable()
    await this.checkTriggerFunction()
    await this.checkTrigger()

    // Check actual data
    const users = await this.checkRecentUsers()
    const profiles = await this.checkProfiles()

    // Analysis
    this.log('\n=== ANALYSIS ===')

    if (users && profiles) {
      const userCount = Array.isArray(users) ? users.length : 0
      const profileCount = Array.isArray(profiles) ? profiles.length : 0

      if (userCount > profileCount) {
        this.log(
          `⚠️  ISSUE FOUND: ${userCount} users but only ${profileCount} profiles`,
          'error'
        )
        this.log('This suggests the trigger is not working properly', 'error')
      } else {
        this.log(
          `✅ User count (${userCount}) matches profile count (${profileCount})`,
          'success'
        )
      }
    }

    this.log('\n=== TEST COMPLETE ===')
  }
}

// Run the tests
if (require.main === module) {
  const tester = new AuthTriggerTest()
  tester.runTests().catch(error => {
    console.error('❌ Test failed:', error)
    process.exit(1)
  })
}

module.exports = AuthTriggerTest
