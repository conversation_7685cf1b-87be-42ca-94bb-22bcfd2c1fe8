-- ===================================================
-- AUTHENTICATION TRIGGER SETUP FOR CLOUD SUPABASE
-- ===================================================
-- Run this SQL in your Supabase SQL Editor to fix GitHub authentication

-- 1. Create the handle_new_user function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  BEGIN
    INSERT INTO public.profiles (id, email, full_name, avatar_url, provider)
    VALUES (
      NEW.id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
      NEW.raw_user_meta_data->>'avatar_url',
      COALESCE(NEW.raw_app_meta_data->>'provider', 'email')
    );
  EXCEPTION
    WHEN OTHERS THEN
      -- Log the error but don't fail the user creation
      RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
  END;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- 2. Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- 3. Create the trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 4. Verify the trigger was created (optional check)
SELECT 
  tgname as trigger_name,
  tgrelid::regclass as table_name,
  tgfoid::regproc as function_name
FROM pg_trigger 
WHERE tgname = 'on_auth_user_created';

-- 5. Test the function manually (optional)
-- This will show you what the function returns for existing users
-- SELECT public.handle_new_user() FROM auth.users LIMIT 1;

-- ===================================================
-- FIX FOR EXISTING USERS WITHOUT PROFILES
-- ===================================================
-- If you have existing users in auth.users but no profiles,
-- run this to create profiles for them:

INSERT INTO public.profiles (id, email, full_name, avatar_url, provider)
SELECT 
  u.id,
  u.email,
  COALESCE(u.raw_user_meta_data->>'full_name', u.raw_user_meta_data->>'name'),
  u.raw_user_meta_data->>'avatar_url',
  COALESCE(u.raw_app_meta_data->>'provider', 'email')
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
WHERE p.id IS NULL;

-- ===================================================
-- VERIFICATION QUERIES
-- ===================================================
-- Check if function exists
SELECT proname, prosrc FROM pg_proc WHERE proname = 'handle_new_user';

-- Check if trigger exists
SELECT tgname, tgrelid::regclass, tgfoid::regproc 
FROM pg_trigger 
WHERE tgname = 'on_auth_user_created';

-- Check users vs profiles count
SELECT 
  (SELECT COUNT(*) FROM auth.users) as users_count,
  (SELECT COUNT(*) FROM public.profiles) as profiles_count;

-- Show recent users and their profile creation
SELECT 
  u.id,
  u.email,
  u.created_at as user_created,
  u.raw_app_meta_data->>'provider' as auth_provider,
  p.created_at as profile_created,
  p.provider as profile_provider
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
ORDER BY u.created_at DESC
LIMIT 10;
